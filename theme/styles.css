@charset "utf-8";

/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

/* Add scroll padding for smooth anchoring */
html {
    scroll-padding-top: 2rem;
}

/* Performance optimizations */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: #2c3e50;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Main Container */
#mainDiv {
    max-width: 1200px;
    margin: 2rem auto;
    background-color: #ffffff;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 4rem);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
/* Header Styles */
#headerDiv {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    position: relative;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

.header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    flex-wrap: wrap;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section img {
    max-height: 80px;
    width: auto;
}

.banner-section {
    flex: 1;
    text-align: right;
}

.banner-section img {
    max-height: 80px;
    width: auto;
}

/* Content Area */
#contentDiv {
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

/* Footer Styles */
#footerDiv {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    margin-top: auto;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-section h4 {
    color: #3498db;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ecf0f1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #3498db;
}

.footer-bottom {
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

/* Modern Layout - Two Column */
.content-wrapper {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    align-items: start;
}

.leftDiv {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 2rem;
}

.leftDiv img {
    width: 100%;
    max-width: 200px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.rightDiv {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}



.layoutTable {
	border-top-width: thin;
	border-right-width: thin;
	border-bottom-width: thin;
	border-left-width: thin;
	border-top-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-left-style: solid;
	width:50%;
	margin-left:25%;
	margin-top:10px;
	margin-right:25%;
}
.layoutTable td
{
		
}
.menu {
	text-align:center;
	vertical-align:middle;
	font-family: Verdana, Geneva, sans-serif;
	font-size: 12px;
	font-weight: bold;
	color: #FFF;
	border-right-style: solid;
	border-right-color: #333;
	border-right-width: thin;
}

.menu a:active
{
	color: #FFFFFF;
	text-decoration:none;
}
.menu a:visited
{
	color: #FFFFFF;
	text-decoration:none;
}
.menu a:link
{
	color: #FFFFFF;
	text-decoration:none;
}
 .menu a:hover
{
	color: #FFFFFF;
	text-decoration:none;
	
}



.line
{
	color: #999;
	background-color: #999;
	height: 1px;
}

body
{
	font-family: Frutiger, sans-serif;
	font-size: 12px;
	color: #555;
	
}
body a
{
	font-family: Frutiger, sans-serif;
	font-size: 12px;
	color: #990000;
}


/* Modern Navigation Menu */
.navMenu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.navMenu li {
    flex: 1;
    position: relative;
}

.navMenu li a {
    display: block;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    text-align: center;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.navMenu li:last-child a {
    border-right: none;
}

.navMenu li a:hover,
.navMenu li a:focus {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.navMenu li a.active {
    background: rgba(255, 255, 255, 0.15);
    font-weight: 600;
}

/* Dropdown Menu Styles */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    width: 100%;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu li a {
    padding: 0.75rem 1rem;
    color: #2c3e50;
    font-size: 0.9rem;
    font-weight: 500;
    border-right: none;
    text-align: left;
    transition: all 0.3s ease;
}

.dropdown-menu li a:hover {
    background: #3498db;
    color: white;
    transform: translateX(5px);
}

/* Mobile Navigation Improvements */
@media (max-width: 768px) {
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    
    .dropdown:hover .dropdown-menu {
        max-height: 300px;
    }
    
    .dropdown-menu li a {
        color: rgba(255, 255, 255, 0.9);
        padding-left: 2rem;
        font-size: 0.85rem;
    }
    
    .dropdown-menu li a:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }
}

/* Modern List Styles */
ul {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.ul_inner {
    list-style: circle;
    padding-left: 2rem;
}

.ul_twoline li {
    padding-bottom: 0.75rem;
    line-height: 1.6;
}

.ul_oneline li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Modern Cards for Publications, Research, etc. */
.card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.card-meta {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><polygon points="36 18 42 18 42 24 36 24"/></g></g></svg>');
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    opacity: 0.95;
    font-weight: 500;
}

.hero-affiliation {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.contact-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid rgba(255,255,255,0.3);
}

.contact-button:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    color: white;
}

.contact-icon {
    font-size: 1.2rem;
}

/* Quick Stats Grid */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Info Cards */
.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.info-card {
    background: white;
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.info-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.card-icon {
    font-size: 2rem;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
}

.card-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.4rem;
}

.position-item, .education-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.3s ease;
}

.position-item:hover, .education-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.position-item:last-child, .education-item:last-child {
    border-bottom: none;
}

.position-title, .degree {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.3rem;
    font-size: 1.1rem;
}

.position-org, .institution {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    margin: 1rem auto;
    border-radius: 2px;
}

/* Highlights Section */
.highlights-section {
    margin-bottom: 4rem;
}

.highlight-categories {
    display: grid;
    gap: 2rem;
}

.highlight-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.highlight-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.highlight-header {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.research-highlight .highlight-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.teaching-highlight .highlight-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.service-highlight .highlight-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.highlight-icon {
    font-size: 2.5rem;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
}

.highlight-header h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.highlight-content {
    padding: 2rem;
}

.key-achievements {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.achievement-row {
    display: flex;
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.achievement-row:hover {
    transform: translateY(-3px);
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.achievement-metric {
    font-size: 2rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 0.5rem;
    display: block;
}

.achievement-desc {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.achievements-list {
    display: grid;
    gap: 1rem;
}

.achievement-item {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
    font-weight: 500;
}

.achievement-item:hover {
    background: #e3f2fd;
    transform: translateX(5px);
    border-left-color: #2980b9;
}

/* Contact Section */
.contact-section {
    margin-bottom: 4rem;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.contact-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.contact-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
}

.contact-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
}

.contact-card h4 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.contact-card p {
    color: #6c757d;
    margin: 0;
    line-height: 1.6;
}

.contact-card a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.contact-card a:hover {
    color: #2980b9;
    text-decoration: underline;
}

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Content Boxes */
.contentBox {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.contentBox h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.profile-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-left: 4px solid #fff;
}

.profile-section h3,
.profile-section .boldHeading1 {
    color: white;
}

/* Modern Quick Finds / Sidebar Navigation */
.quickfindsBox {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-top: 2rem;
    overflow: hidden;
}

.quickfindsHeader {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 1rem;
    font-weight: 600;
    text-align: center;
    font-size: 1rem;
}

.quickfindsLinks {
    padding: 0;
}

.quickfindsLinks ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quickfindsLinks li {
    border-bottom: 1px solid #ecf0f1;
}

.quickfindsLinks li:last-child {
    border-bottom: none;
}

.quickfindsLinks a {
    display: block;
    padding: 0.75rem 1rem;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.quickfindsLinks a:hover {
    background: #3498db;
    color: white;
    transform: translateX(5px);
}

.quickfindsLinks li a:hover
{
	text-decoration:underline;

	color:#F03;
}
.quickfindsLinks a:active
{
	color:#990000;
	text-decoration:none;
}
.quickfindsLinks a:link
{
	color:#990000;
	text-decoration:none;
}
.quickfindsLinks a:visited
{
	color:#990000;
	text-decoration:none;
}

.quickfindsBox ul
{
	padding-left:10px;
	list-style:none;
}
.boldHeading
{
 font-weight:bold;	
}

.boldHeading1 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
    #mainDiv {
        margin: 1rem;
        border-radius: 15px;
    }

    #headerDiv {
        border-top-left-radius: 15px;
        border-top-right-radius: 15px;
    }

    .header-top {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        padding: 1rem;
    }

    .banner-section {
        text-align: center;
    }

    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .leftDiv {
        position: static;
        order: 2;
        text-align: center;
    }

    .rightDiv {
        order: 1;
    }

    #contentDiv {
        padding: 1rem;
    }

    .navMenu {
        flex-direction: column;
    }

    .navMenu li a {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem;
    }

    .navMenu li:last-child a {
        border-bottom: none;
    }

    /* Hero Section Mobile */
    .hero-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-affiliation {
        font-size: 1rem;
    }

    .hero-section {
        padding: 2rem 1.5rem;
    }

    /* Quick Stats Mobile */
    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    /* Info Cards Mobile */
    .info-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-icon {
        font-size: 1.5rem;
    }

    /* Highlights Mobile */
    .section-title {
        font-size: 2rem;
    }

    .highlight-header {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .highlight-icon {
        font-size: 2rem;
    }

    .highlight-header h3 {
        font-size: 1.4rem;
    }

    .highlight-content {
        padding: 1.5rem;
    }

    .key-achievements {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .achievement-row {
        padding: 1rem;
    }

    .achievement-metric {
        font-size: 1.5rem;
    }

    /* Contact Grid Mobile */
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-card {
        padding: 1.5rem;
    }

    .contact-icon {
        font-size: 2rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }

    .research-areas {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .research-stats {
        grid-template-columns: 1fr;
    }

    .lab-info {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    /* Featured Grid Mobile */
    .featured-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .featured-card {
        padding: 1.5rem;
    }

    .featured-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .featured-card h3 {
        font-size: 1.2rem;
    }

    .featured-card p {
        font-size: 0.95rem;
    }

    .featured-stats {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 480px) {
    #mainDiv {
        margin: 0;
        border-radius: 0;
        box-shadow: none;
        min-height: 100vh;
    }

    .contentBox {
        padding: 1rem;
    }

    .card {
        padding: 1rem;
    }

    /* Hero Section Extra Small */
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-section {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
    }

    .contact-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Quick Stats Extra Small */
    .quick-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    /* Highlights Extra Small */
    .section-title {
        font-size: 1.6rem;
        margin-bottom: 2rem;
    }

    .highlight-header {
        padding: 1rem;
    }

    .highlight-content {
        padding: 1rem;
    }

    .achievement-row {
        padding: 0.75rem;
    }

    .achievement-metric {
        font-size: 1.3rem;
    }

    .achievement-desc {
        font-size: 0.8rem;
    }

    .achievement-item {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    /* Contact Cards Extra Small */
    .contact-card {
        padding: 1rem;
    }

    .contact-icon {
        font-size: 1.8rem;
    }

    .contact-card h4 {
        font-size: 1.1rem;
    }

    .contact-card p {
        font-size: 0.9rem;
    }

    .research-card {
        padding: 1rem;
    }

    .impact-item {
        padding: 1rem;
    }

    .lab-item {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.1rem; }

    .header-top {
        padding: 0.5rem;
    }

    .logo-section img,
    .banner-section img {
        max-height: 60px;
    }

    /* Featured Cards Extra Small */
    .featured-grid {
        gap: 1rem;
    }

    .featured-card {
        padding: 1.25rem;
        border-radius: 15px;
    }

    .featured-icon {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    .featured-card h3 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .featured-card p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .featured-stats {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }

    /* Quick Navigation Mobile */
    .quickfindsBox {
        margin-top: 1rem;
    }

    .quickfindsLinks a {
        padding: 0.6rem 0.75rem;
        font-size: 0.85rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Email and Contact Styles */
.emailStyle {
    color: #3498db;
    font-weight: 500;
}

.emailStyle:hover {
    color: #2980b9;
}

/* Search Container */
.search-container {
    margin: 1.5rem 0;
}

.search-container input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-container input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Publications List */
.publications-list {
    display: grid;
    gap: 1rem;
}

.publication-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.publication-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.1);
}

.publication-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.publication-authors {
    color: #7f8c8d;
    font-style: italic;
    margin-bottom: 0.5rem;
}

.publication-journal {
    color: #3498db;
    font-weight: 500;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Research Areas */
.research-areas {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.research-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.research-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.research-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.research-card h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.research-card ul {
    list-style: none;
    padding: 0;
}

.research-card li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
    position: relative;
    padding-left: 1.5rem;
}

.research-card li:before {
    content: "▸";
    color: #3498db;
    position: absolute;
    left: 0;
}

.research-card li:last-child {
    border-bottom: none;
}

/* Research Stats */
.research-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

/* Impact Highlights */
.impact-highlights {
    display: grid;
    gap: 1.5rem;
    margin-top: 1rem;
}

.impact-item {
    background: #f8f9fa;
    border-left: 4px solid #3498db;
    padding: 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.impact-item:hover {
    background: #e8f4fd;
    transform: translateX(5px);
}

.impact-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

/* Lab Information */
.lab-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.lab-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
}

.lab-item h4 {
    color: white;
    margin-bottom: 1rem;
}

/* Teaching Styles */
.teaching-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.philosophy-section {
    display: grid;
    gap: 1.5rem;
    margin-top: 1rem;
}

.philosophy-item {
    background: #f8f9fa;
    border-left: 4px solid #e74c3c;
    padding: 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.philosophy-item:hover {
    background: #fdf2f2;
    transform: translateX(5px);
}

.philosophy-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Course Cards */
.course-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.course-card {
    background: white;
    border: 2px solid #3498db;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.2);
}

.course-card.graduate {
    border-color: #9b59b6;
}

.course-card.graduate:hover {
    box-shadow: 0 10px 25px rgba(155, 89, 182, 0.2);
}

.course-code {
    background: #3498db;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.course-card.graduate .course-code {
    background: #9b59b6;
}

.course-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.course-description {
    color: #7f8c8d;
    line-height: 1.5;
}

/* Achievement List */
.achievement-list {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.achievement-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #28a745;
    padding: 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.achievement-item:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    transform: translateX(5px);
}

.achievement-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Diversity Stats */
.diversity-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.diversity-item {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
    color: white;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.diversity-item:hover {
    transform: translateY(-3px);
}

.diversity-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.diversity-label {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.3;
}

/* Featured Links Section */
.featured-links-section {
    margin-bottom: 4rem;
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.featured-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.featured-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.featured-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

.publications-card::before {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.research-card::before {
    background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.labs-card::before {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.team-card::before {
    background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
}

.teaching-card::before {
    background: linear-gradient(90deg, #fa709a 0%, #fee140 100%);
}

.awards-card::before {
    background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
}

.featured-card:hover::before {
    height: 6px;
}

.featured-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
}

.featured-card h3 {
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.featured-card p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.featured-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 0.75rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #3498db;
    text-align: center;
    border: 1px solid #e9ecef;
}

/* Featured Cards Specific Colors */
.publications-card:hover {
    border-color: #667eea;
}

.publications-card:hover .featured-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.research-card:hover {
    border-color: #f093fb;
}

.research-card:hover .featured-stats {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.labs-card:hover {
    border-color: #4facfe;
}

.labs-card:hover .featured-stats {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.team-card:hover {
    border-color: #43e97b;
}

.team-card:hover .featured-stats {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.teaching-card:hover {
    border-color: #fa709a;
}

.teaching-card:hover .featured-stats {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.awards-card:hover {
    border-color: #a8edea;
}

.awards-card:hover .featured-stats {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2c3e50;
}
